const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = 'https://evauqytvhvjuryhhfjoy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV2YXVxeXR2aHZqdXJ5aGhmam95Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUxMTgzMzUsImV4cCI6MjA3MDY5NDMzNX0.TGKvH5lRC-Qb-BJdqCFn7ybwDR1mZCb-WLH0-yzr15A';

async function testFrontendFlow() {
  console.log('🔍 Testing complete frontend authentication flow...');
  console.log('');

  try {
    // 1. Create client like the frontend does
    console.log('1. Creating Supabase client (like frontend)...');
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // 2. Check initial session (should be null)
    console.log('2. Checking initial session...');
    const { data: { session: initialSession } } = await supabase.auth.getSession();
    console.log('   Initial session exists:', !!initialSession);

    // 3. Sign in with password
    console.log('3. Signing in with password...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'test123456'
    });

    if (signInError) {
      console.error('❌ Sign in failed:', signInError.message);
      return;
    }

    console.log('✅ Sign in successful');
    console.log('   User ID:', signInData.user.id);
    console.log('   Access token exists:', !!signInData.session?.access_token);

    // 4. Verify session after sign in
    console.log('4. Verifying session after sign in...');
    const { data: { session: postSignInSession } } = await supabase.auth.getSession();
    console.log('   Post sign-in session exists:', !!postSignInSession);
    console.log('   Post sign-in access token exists:', !!postSignInSession?.access_token);

    // 5. Test API call like the frontend does
    console.log('5. Testing API call with session token...');
    
    // Simulate the ApiClient.getAuthHeaders() method
    const headers = {
      "Content-Type": "application/json",
      ...(postSignInSession?.access_token && {
        Authorization: `Bearer ${postSignInSession.access_token}`,
      }),
    };

    console.log('   Headers include Authorization:', !!headers.Authorization);

    // Make the API call
    const response = await fetch('http://localhost:3002/api/businesses/my-businesses', {
      method: 'GET',
      headers,
    });

    console.log('   API Response status:', response.status);
    console.log('   API Response ok:', response.ok);

    if (response.ok) {
      const businesses = await response.json();
      console.log('✅ API call successful');
      console.log(`   Returned ${businesses.length} businesses`);
      console.log('   Business names:', businesses.map(b => b.business_name));
    } else {
      const errorText = await response.text();
      console.log('❌ API call failed');
      console.log('   Error:', errorText);
    }

    // 6. Test what happens if we call the API immediately after sign in
    console.log('6. Testing immediate API call after sign in...');
    
    // Wait a moment to ensure session is fully established
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const { data: { session: freshSession } } = await supabase.auth.getSession();
    const freshHeaders = {
      "Content-Type": "application/json",
      ...(freshSession?.access_token && {
        Authorization: `Bearer ${freshSession.access_token}`,
      }),
    };

    const freshResponse = await fetch('http://localhost:3002/api/businesses/my-businesses', {
      method: 'GET',
      headers: freshHeaders,
    });

    console.log('   Fresh API Response status:', freshResponse.status);
    
    if (freshResponse.ok) {
      const freshBusinesses = await freshResponse.json();
      console.log('✅ Fresh API call successful');
      console.log(`   Returned ${freshBusinesses.length} businesses`);
    } else {
      const freshErrorText = await freshResponse.text();
      console.log('❌ Fresh API call failed');
      console.log('   Error:', freshErrorText);
    }

  } catch (error) {
    console.error('❌ Error in frontend flow test:', error);
  }
}

testFrontendFlow().catch(console.error);
