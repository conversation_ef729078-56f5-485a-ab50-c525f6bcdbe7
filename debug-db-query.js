const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = 'https://evauqytvhvjuryhhfjoy.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV2YXVxeXR2aHZqdXJ5aGhmam95Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTExODMzNSwiZXhwIjoyMDcwNjk0MzM1fQ.cHfjJygXU00hNCz4uSPsO0mDUKKCGG3GHG1nkW3m_BQ';

async function debugDbQuery() {
  console.log('🔍 Debugging database query for user businesses...');
  console.log('');

  try {
    // Initialize Supabase client with service role
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    const currentUserId = '6cebe701-ca56-493c-a087-150a81071ab8';
    console.log('Current user ID:', currentUserId);
    console.log('');

    // 1. Check if user exists in auth
    console.log('1. Checking user in auth...');
    try {
      const { data: userData, error: userError } = await supabase.auth.admin.getUserById(currentUserId);
      if (userError) {
        console.log('❌ User not found in auth:', userError.message);
      } else {
        console.log('✅ User found in auth:', userData.user.email);
      }
    } catch (err) {
      console.log('❌ Error fetching user:', err.message);
    }
    console.log('');

    // 2. Check all business_profiles table
    console.log('2. Checking all business_profiles...');
    const { data: allProfiles, error: allProfilesError } = await supabase
      .from('business_profiles')
      .select('*');

    if (allProfilesError) {
      console.error('❌ Error fetching all business profiles:', allProfilesError);
    } else {
      console.log(`✅ Total business profiles in database: ${allProfiles.length}`);
      allProfiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. User ID: ${profile.user_id}, Business ID: ${profile.business_id}`);
      });
    }
    console.log('');

    // 3. Check business_profiles for current user
    console.log('3. Checking business_profiles for current user...');
    const { data: userProfiles, error: userProfilesError } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', currentUserId);

    if (userProfilesError) {
      console.error('❌ Error fetching user business profiles:', userProfilesError);
    } else {
      console.log(`✅ Business profiles for current user: ${userProfiles.length}`);
      userProfiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. Profile ID: ${profile.id}, Business ID: ${profile.business_id}`);
      });
    }
    console.log('');

    // 4. Check all businesses table
    console.log('4. Checking all businesses...');
    const { data: allBusinesses, error: allBusinessesError } = await supabase
      .from('businesses')
      .select('*');

    if (allBusinessesError) {
      console.error('❌ Error fetching all businesses:', allBusinessesError);
    } else {
      console.log(`✅ Total businesses in database: ${allBusinesses.length}`);
      allBusinesses.forEach((business, index) => {
        console.log(`   ${index + 1}. Business ID: ${business.id}, Name: ${business.business_name}`);
      });
    }
    console.log('');

    // 5. Test the exact query used in getUserBusinesses
    console.log('5. Testing the exact query used in getUserBusinesses...');
    const { data: joinedData, error: joinedError } = await supabase
      .from('business_profiles')
      .select(`
        id,
        user_id,
        business_id,
        created_at,
        businesses (
          id,
          google_place_id,
          business_name,
          business_address,
          phone_number,
          website,
          last_fetch_time,
          rating,
          total_reviews,
          categories,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', currentUserId);

    if (joinedError) {
      console.error('❌ Error with joined query:', joinedError);
    } else {
      console.log(`✅ Joined query returned ${joinedData.length} results for current user`);
      joinedData.forEach((item, index) => {
        console.log(`   ${index + 1}. Profile ID: ${item.id}`);
        console.log(`      Business data:`, item.businesses ? 'Present' : 'Missing');
        if (item.businesses) {
          console.log(`      Business name: ${item.businesses.business_name}`);
        }
      });
    }
    console.log('');

    // 6. If no data for current user, let's create some test data
    if (userProfiles.length === 0 && allBusinesses.length > 0) {
      console.log('6. Current user has no businesses. Creating test business profile...');
      
      // Use the first available business
      const testBusiness = allBusinesses[0];
      console.log(`   Using business: ${testBusiness.business_name} (ID: ${testBusiness.id})`);
      
      const { data: newProfile, error: createError } = await supabase
        .from('business_profiles')
        .insert({
          user_id: currentUserId,
          business_id: testBusiness.id
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Error creating business profile:', createError);
      } else {
        console.log('✅ Successfully created business profile:', newProfile.id);
        
        // Test the query again
        console.log('   Testing query again...');
        const { data: retestData, error: retestError } = await supabase
          .from('business_profiles')
          .select(`
            id,
            user_id,
            business_id,
            created_at,
            businesses (
              id,
              google_place_id,
              business_name,
              business_address,
              phone_number,
              website,
              last_fetch_time,
              rating,
              total_reviews,
              categories,
              created_at,
              updated_at
            )
          `)
          .eq('user_id', currentUserId);

        if (retestError) {
          console.error('   ❌ Error with retest query:', retestError);
        } else {
          console.log(`   ✅ Retest query returned ${retestData.length} results`);
          retestData.forEach((item, index) => {
            console.log(`      ${index + 1}. Business: ${item.businesses?.business_name || 'N/A'}`);
          });
        }
      }
    }

    console.log('');
    console.log('🎉 Debug completed!');

  } catch (error) {
    console.error('❌ Error in debug process:', error);
  }
}

debugDbQuery().catch(console.error);
