const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = 'https://evauqytvhvjuryhhfjoy.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV2YXVxeXR2aHZqdXJ5aGhmam95Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTExODMzNSwiZXhwIjoyMDcwNjk0MzM1fQ.cHfjJygXU00hNCz4uSPsO0mDUKKCGG3GHG1nkW3m_BQ';

async function fixUserBusinessAssociation() {
  console.log('🔧 Fixing user business association...');
  console.log('');

  try {
    // Initialize Supabase client with service role
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    const originalUserId = 'a4483bb3-5ecb-41bc-9762-9024418eab26';
    const newUserId = '6cebe701-ca56-493c-a087-150a81071ab8';

    // 1. Check both users
    console.log('1. Checking both user accounts...');
    
    const { data: originalUser, error: originalUserError } = await supabase.auth.admin.getUserById(originalUserId);
    const { data: newUser, error: newUserError } = await supabase.auth.admin.getUserById(newUserId);

    if (originalUserError) {
      console.error('❌ Error fetching original user:', originalUserError);
      return;
    }

    if (newUserError) {
      console.error('❌ Error fetching new user:', newUserError);
      return;
    }

    console.log('✅ Original user:', originalUser.user.email);
    console.log('✅ New user:', newUser.user.email);
    console.log('');

    // 2. Check business profiles for both users
    console.log('2. Checking business profiles...');
    
    const { data: originalProfiles, error: originalProfilesError } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', originalUserId);

    const { data: newProfiles, error: newProfilesError } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', newUserId);

    if (originalProfilesError) {
      console.error('❌ Error fetching original profiles:', originalProfilesError);
      return;
    }

    if (newProfilesError) {
      console.error('❌ Error fetching new profiles:', newProfilesError);
      return;
    }

    console.log(`✅ Original user has ${originalProfiles.length} business profiles`);
    console.log(`✅ New user has ${newProfiles.length} business profiles`);
    console.log('');

    // 3. Transfer business profiles from original user to new user
    if (originalProfiles.length > 0) {
      console.log('3. Transferring business profiles to new user...');
      
      for (const profile of originalProfiles) {
        console.log(`   Transferring business profile ${profile.id}...`);
        
        // Update the user_id to the new user
        const { error: updateError } = await supabase
          .from('business_profiles')
          .update({ user_id: newUserId })
          .eq('id', profile.id);

        if (updateError) {
          console.error(`❌ Error updating profile ${profile.id}:`, updateError);
        } else {
          console.log(`✅ Successfully transferred profile ${profile.id}`);
        }
      }
    }

    // 4. Also transfer any reviews if they exist
    console.log('4. Checking and transferring reviews...');
    
    const { data: originalReviews, error: originalReviewsError } = await supabase
      .from('reviews')
      .select('id')
      .eq('user_id', originalUserId);

    if (originalReviewsError) {
      console.error('❌ Error fetching original reviews:', originalReviewsError);
    } else {
      console.log(`✅ Original user has ${originalReviews.length} reviews`);
      
      if (originalReviews.length > 0) {
        const { error: updateReviewsError } = await supabase
          .from('reviews')
          .update({ user_id: newUserId })
          .eq('user_id', originalUserId);

        if (updateReviewsError) {
          console.error('❌ Error transferring reviews:', updateReviewsError);
        } else {
          console.log(`✅ Successfully transferred ${originalReviews.length} reviews`);
        }
      }
    }

    // 5. Verify the transfer
    console.log('5. Verifying the transfer...');
    
    const { data: verifyProfiles, error: verifyError } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', newUserId);

    if (verifyError) {
      console.error('❌ Error verifying transfer:', verifyError);
    } else {
      console.log(`✅ New user now has ${verifyProfiles.length} business profiles`);
    }

    console.log('');
    console.log('🎉 Transfer completed! The new user should now see the businesses.');

  } catch (error) {
    console.error('❌ Error in transfer process:', error);
  }
}

fixUserBusinessAssociation().catch(console.error);
