const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = 'https://evauqytvhvjuryhhfjoy.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV2YXVxeXR2aHZqdXJ5aGhmam95Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTExODMzNSwiZXhwIjoyMDcwNjk0MzM1fQ.cHfjJygXU00hNCz4uSPsO0mDUKKCGG3GHG1nkW3m_BQ';

async function findBusinessOwner() {
  console.log('🔍 Finding business owner...');
  console.log('');

  try {
    // Initialize Supabase client with service role
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // 1. Find all business profiles
    console.log('1. Finding all business profiles...');
    
    const { data: allProfiles, error: profilesError } = await supabase
      .from('business_profiles')
      .select(`
        id,
        user_id,
        business_id,
        created_at,
        businesses (
          business_name,
          google_place_id
        )
      `);

    if (profilesError) {
      console.error('❌ Error fetching business profiles:', profilesError);
      return;
    }

    console.log(`✅ Found ${allProfiles.length} business profiles`);
    
    if (allProfiles.length === 0) {
      console.log('❌ No business profiles found');
      return;
    }

    // 2. Show all profiles
    console.log('2. Business profiles details:');
    allProfiles.forEach((profile, index) => {
      console.log(`   ${index + 1}. Profile ID: ${profile.id}`);
      console.log(`      User ID: ${profile.user_id}`);
      console.log(`      Business ID: ${profile.business_id}`);
      console.log(`      Business Name: ${profile.businesses?.business_name || 'N/A'}`);
      console.log(`      Created: ${profile.created_at}`);
      console.log('');
    });

    // 3. Get unique user IDs
    const userIds = [...new Set(allProfiles.map(p => p.user_id))];
    console.log(`3. Found ${userIds.length} unique user(s) with businesses:`);
    
    for (const userId of userIds) {
      console.log(`   User ID: ${userId}`);
      
      // Try to get user details
      try {
        const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
        if (userError) {
          console.log(`      ❌ User not found in auth: ${userError.message}`);
        } else {
          console.log(`      ✅ Email: ${userData.user.email}`);
        }
      } catch (err) {
        console.log(`      ❌ Error fetching user: ${err.message}`);
      }
    }

    // 4. Current signed-in user
    const currentUserId = '6cebe701-ca56-493c-a087-150a81071ab8';
    console.log('');
    console.log('4. Current signed-in user:');
    console.log(`   User ID: ${currentUserId}`);
    
    try {
      const { data: currentUserData, error: currentUserError } = await supabase.auth.admin.getUserById(currentUserId);
      if (currentUserError) {
        console.log(`   ❌ Current user not found: ${currentUserError.message}`);
      } else {
        console.log(`   ✅ Email: ${currentUserData.user.email}`);
      }
    } catch (err) {
      console.log(`   ❌ Error fetching current user: ${err.message}`);
    }

    // 5. Transfer businesses to current user if needed
    if (userIds.length > 0 && !userIds.includes(currentUserId)) {
      console.log('');
      console.log('5. Transferring businesses to current user...');
      
      for (const profile of allProfiles) {
        console.log(`   Transferring profile ${profile.id} to current user...`);
        
        const { error: updateError } = await supabase
          .from('business_profiles')
          .update({ user_id: currentUserId })
          .eq('id', profile.id);

        if (updateError) {
          console.error(`   ❌ Error updating profile ${profile.id}:`, updateError);
        } else {
          console.log(`   ✅ Successfully transferred profile ${profile.id}`);
        }
      }

      // Also transfer any reviews
      for (const userId of userIds) {
        const { data: reviews, error: reviewsError } = await supabase
          .from('reviews')
          .select('id')
          .eq('user_id', userId);

        if (!reviewsError && reviews.length > 0) {
          console.log(`   Transferring ${reviews.length} reviews from user ${userId}...`);
          
          const { error: updateReviewsError } = await supabase
            .from('reviews')
            .update({ user_id: currentUserId })
            .eq('user_id', userId);

          if (updateReviewsError) {
            console.error(`   ❌ Error transferring reviews:`, updateReviewsError);
          } else {
            console.log(`   ✅ Successfully transferred ${reviews.length} reviews`);
          }
        }
      }

      console.log('');
      console.log('🎉 Transfer completed! Current user should now see the businesses.');
    } else if (userIds.includes(currentUserId)) {
      console.log('');
      console.log('✅ Current user already has businesses - no transfer needed.');
    }

  } catch (error) {
    console.error('❌ Error in process:', error);
  }
}

findBusinessOwner().catch(console.error);
