#!/usr/bin/env node

/**
 * Test login functionality and check session state
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  log('❌ Missing required environment variables:', colors.red);
  process.exit(1);
}

// Initialize Supabase client (client-side, like the frontend)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testLogin() {
  try {
    log('🔐 Testing login functionality...', colors.blue);
    
    // Test login with the test user
    log('📧 Signing <NAME_EMAIL>...', colors.yellow);
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (authError) {
      log(`❌ Login failed: ${authError.message}`, colors.red);
      return;
    }
    
    log('✅ Login successful!', colors.green);
    log(`   User ID: ${authData.user.id}`, colors.green);
    log(`   Email: ${authData.user.email}`, colors.green);
    log(`   Access Token: ${authData.session.access_token.substring(0, 50)}...`, colors.green);
    
    // Test API call with this session
    log('\n🌐 Testing business API with authenticated session...', colors.blue);
    
    const response = await fetch('http://localhost:3001/api/businesses/my-businesses', {
      headers: {
        'Authorization': `Bearer ${authData.session.access_token}`,
        'Content-Type': 'application/json',
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      log(`❌ API request failed: ${response.status} ${response.statusText}`, colors.red);
      log(`   Response: ${errorText}`, colors.red);
      return;
    }
    
    const businesses = await response.json();
    log(`✅ API request successful!`, colors.green);
    log(`   Returned ${businesses.length} businesses`, colors.green);
    
    if (businesses.length > 0) {
      log('📊 First business:', colors.cyan);
      console.log(JSON.stringify(businesses[0], null, 2));
    }
    
    // Clean up - sign out
    log('\n🚪 Signing out...', colors.yellow);
    await supabase.auth.signOut();
    log('✅ Signed out successfully', colors.green);
    
    return businesses;
    
  } catch (error) {
    log(`❌ Test failed: ${error.message}`, colors.red);
    console.error('Full error:', error);
  }
}

async function main() {
  try {
    log('🚀 Starting login and API test...', colors.bright);
    
    const result = await testLogin();
    
    if (result && result.length > 0) {
      log('\n🎉 Test Summary:', colors.cyan);
      log('   ✅ Login works correctly', colors.green);
      log('   ✅ API authentication works', colors.green);
      log('   ✅ Business data is available', colors.green);
      log('\n💡 The issue is likely in frontend session management or auth context.', colors.yellow);
    } else {
      log('\n❌ Test failed - authentication or business fetching has issues', colors.red);
    }
    
  } catch (error) {
    log(`\n❌ Test suite failed: ${error.message}`, colors.red);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}