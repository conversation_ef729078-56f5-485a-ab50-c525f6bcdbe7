#!/usr/bin/env node

/**
 * Check which users have business profiles
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  log('❌ Missing required environment variables:', colors.red);
  process.exit(1);
}

// Initialize Supabase client with service role
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function checkUsers() {
  try {
    log('👥 Checking all users and their business profiles...', colors.blue);
    
    // Get all users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      throw new Error(`Failed to list users: ${usersError.message}`);
    }
    
    log(`📊 Found ${users.users.length} users:`, colors.cyan);
    for (const user of users.users) {
      log(`   - ${user.email} (${user.id})`, colors.yellow);
    }
    
    // Get all business profiles
    log('\n🏢 Checking business profiles...', colors.blue);
    const { data: profiles, error: profilesError } = await supabase
      .from('business_profiles')
      .select(`
        id,
        user_id,
        business_id,
        created_at,
        businesses (
          id,
          google_place_id,
          business_name,
          business_address,
          rating,
          total_reviews,
          categories
        )
      `);
    
    if (profilesError) {
      throw new Error(`Failed to get business profiles: ${profilesError.message}`);
    }
    
    log(`📊 Found ${profiles.length} business profiles:`, colors.cyan);
    
    // Group profiles by user
    const profilesByUser = {};
    for (const profile of profiles) {
      if (!profilesByUser[profile.user_id]) {
        profilesByUser[profile.user_id] = [];
      }
      profilesByUser[profile.user_id].push(profile);
    }
    
    // Show profiles for each user
    for (const user of users.users) {
      const userProfiles = profilesByUser[user.id] || [];
      log(`\n👤 ${user.email} (${user.id}):`, colors.green);
      
      if (userProfiles.length === 0) {
        log(`   ❌ No business profiles`, colors.red);
      } else {
        log(`   ✅ ${userProfiles.length} business profile(s):`, colors.green);
        for (const profile of userProfiles) {
          const business = profile.businesses;
          if (business) {
            log(`      • ${business.business_name}`, colors.cyan);
            log(`        - Place ID: ${business.google_place_id}`, colors.yellow);
            log(`        - Rating: ${business.rating || 'N/A'}`, colors.yellow);
            log(`        - Reviews: ${business.total_reviews || 'N/A'}`, colors.yellow);
          } else {
            log(`      • Business data missing for profile ${profile.id}`, colors.red);
          }
        }
      }
    }
    
    return { users: users.users, profiles };
    
  } catch (error) {
    log(`❌ Check failed: ${error.message}`, colors.red);
    console.error('Full error:', error);
    throw error;
  }
}

async function main() {
  try {
    log('🚀 Starting user and business profile check...', colors.bright);
    
    const result = await checkUsers();
    
    log('\n📋 Summary:', colors.cyan);
    log(`   Total users: ${result.users.length}`, colors.blue);
    log(`   Total business profiles: ${result.profiles.length}`, colors.blue);
    
    // Find users with business profiles
    const usersWithProfiles = result.users.filter(user => 
      result.profiles.some(profile => profile.user_id === user.id)
    );
    
    if (usersWithProfiles.length > 0) {
      log('\n💡 To test the dashboard, log in as one of these users:', colors.yellow);
      for (const user of usersWithProfiles) {
        log(`   📧 ${user.email}`, colors.green);
      }
    } else {
      log('\n⚠️ No users have business profiles! Run onboarding or migration script.', colors.yellow);
    }
    
  } catch (error) {
    log(`\n❌ Check failed: ${error.message}`, colors.red);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}