#!/usr/bin/env node

/**
 * Test authenticated business fetching to debug the issue
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  log('❌ Missing required environment variables:', colors.red);
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function testBusinessService() {
  try {
    log('🧪 Testing BusinessService getUserBusinesses...', colors.blue);
    
    // Get the test user from the database
    log('👤 Finding test user...', colors.yellow);
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      throw new Error(`Failed to list users: ${usersError.message}`);
    }
    
    const testUser = users.users.find(u => u.email === '<EMAIL>');
    if (!testUser) {
      log('❌ Test user not found. Run migration script first:', colors.red);
      log('   node scripts/migrate-example-data.js', colors.yellow);
      return;
    }
    
    log(`✅ Found test user: ${testUser.email} (${testUser.id})`, colors.green);
    
    // Import and test the BusinessService directly
    const { BusinessService } = require('../src/lib/services/business.js');
    const businessService = new BusinessService(process.env.APIFY_API_TOKEN);
    
    // Test getUserBusinesses
    log('🏢 Testing getUserBusinesses...', colors.yellow);
    const mockUser = {
      id: testUser.id,
      email: testUser.email,
      aud: 'authenticated',
    };
    
    const businesses = await businessService.getUserBusinesses(mockUser);
    
    log(`✅ getUserBusinesses returned ${businesses.length} businesses`, colors.green);
    
    if (businesses.length > 0) {
      log('📊 Sample business:', colors.cyan);
      console.log(JSON.stringify(businesses[0], null, 2));
    } else {
      log('📊 No businesses found for this user', colors.yellow);
    }
    
    return businesses;
    
  } catch (error) {
    log(`❌ BusinessService test failed: ${error.message}`, colors.red);
    console.error('Full error:', error);
    throw error;
  }
}

async function testApiEndpoint() {
  try {
    log('\n🌐 Testing API endpoint with simulated auth...', colors.blue);
    
    // Get test user
    const { data: users } = await supabase.auth.admin.listUsers();
    const testUser = users.users.find(u => u.email === '<EMAIL>');
    
    if (!testUser) {
      log('❌ Test user not found', colors.red);
      return;
    }
    
    // Create a JWT token for the test user
    log('🔑 Creating JWT token...', colors.yellow);
    const { data: tokenData, error: tokenError } = await supabase.auth.admin.generateAccessToken(testUser.id);
    
    if (tokenError) {
      log(`❌ Failed to generate token: ${tokenError.message}`, colors.red);
      return;
    }
    
    log('✅ JWT token generated', colors.green);
    
    // Test the API endpoint with authentication
    const response = await fetch('http://localhost:3001/api/businesses/my-businesses', {
      headers: {
        'Authorization': `Bearer ${tokenData.access_token}`,
        'Content-Type': 'application/json',
      }
    });
    
    const responseText = await response.text();
    
    if (!response.ok) {
      log(`❌ API request failed: ${response.status} ${response.statusText}`, colors.red);
      log(`   Response: ${responseText}`, colors.red);
      return;
    }
    
    const businesses = JSON.parse(responseText);
    log(`✅ API returned ${businesses.length} businesses`, colors.green);
    
    if (businesses.length > 0) {
      log('📊 Sample business from API:', colors.cyan);
      console.log(JSON.stringify(businesses[0], null, 2));
    }
    
    return businesses;
    
  } catch (error) {
    log(`❌ API test failed: ${error.message}`, colors.red);
    console.error('Full error:', error);
  }
}

async function main() {
  try {
    log('🚀 Starting authenticated business fetch test...', colors.bright);
    
    // Test 1: Direct service test
    const serviceResult = await testBusinessService();
    
    // Test 2: API endpoint test
    const apiResult = await testApiEndpoint();
    
    log('\n📋 Test Summary:', colors.cyan);
    log(`   Service layer: ${serviceResult ? serviceResult.length : 0} businesses`, colors.blue);
    log(`   API endpoint: ${apiResult ? apiResult.length : 0} businesses`, colors.blue);
    
    if (serviceResult && serviceResult.length > 0 && apiResult && apiResult.length > 0) {
      log('\n🎉 Both tests passed! Business fetching works when authenticated.', colors.green);
      log('\n💡 The issue is likely authentication on the frontend:', colors.yellow);
      log('   1. Check if user is properly signed in', colors.yellow);
      log('   2. Check if JWT token is being sent correctly', colors.yellow);
      log('   3. Check browser network tab for actual requests', colors.yellow);
    } else {
      log('\n❌ Tests failed - business fetching has issues', colors.red);
    }
    
  } catch (error) {
    log(`\n❌ Test suite failed: ${error.message}`, colors.red);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}