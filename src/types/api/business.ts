// Business-related API types

export interface BusinessSearchResponse {
  place_id: string;
  name: string;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  total_reviews?: number;
  types?: string[];
}

export interface BusinessInfo {
  id: string;
  user_id: string;
  google_place_id: string;
  business_name: string;
  business_address?: string;
  phone_number?: string;
  website?: string;
  created_at: string;
  updated_at?: string;
}
