import { supabase } from "./supabase";
import type {
  BusinessSearchResponse,
  BusinessInfo,
  PaginatedReviewsResponse,
  Summary,
} from '@/types/api';
import { getUserLanguageForApi } from './language-utils';

// Use Next.js API routes instead of external backend
const API_BASE_URL = "";

class ApiClient {
  private async getAuthHeaders() {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return {
      "Content-Type": "application/json",
      ...(session?.access_token && {
        Authorization: `Bearer ${session.access_token}`,
      }),
    };
  }

  async get(endpoint: string) {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return response.json();
  }

  async post(endpoint: string, data: unknown) {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return response.json();
  }

  async delete(endpoint: string) {
    const headers = await this.getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: "DELETE",
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.statusText}`);
    }

    return response.json();
  }

  // Business Management Methods
  async searchBusinessFromUrl(googleMapsUrl: string): Promise<BusinessSearchResponse> {
    const language = getUserLanguageForApi();
    return this.post("/api/businesses/search-from-url", {
      google_maps_url: googleMapsUrl,
      language: language,
    });
  }

  async saveBusiness(businessData: BusinessSearchResponse): Promise<{
    message: string;
    business_id: string;
    place_id: string;
    name: string;
  }> {
    return this.post("/api/businesses/save", businessData);
  }

  async getMyBusinesses(): Promise<BusinessInfo[]> {
    return this.get("/api/businesses/my-businesses");
  }

  async removeBusiness(businessId: string): Promise<{ message: string }> {
    return this.delete(`/api/businesses/my-businesses/${businessId}`);
  }

  // Reviews Management Methods
  async fetchReviews(placeId: string, businessName?: string): Promise<{
    message: string;
    business_name: string;
    business_id: string;
    place_id: string;
    total_reviews: number;
    new_reviews: number;
    existing_reviews: number;
    last_fetch_time: string;
  }> {
    return this.post("/api/reviews/fetch", {
      google_place_id: placeId,
      business_name: businessName,
    });
  }

  async getReviewsPaginated(params: {
    google_place_id?: string;
    page?: number;
    limit?: number;
    start_date?: string;
    end_date?: string;
    sentiment?: string;
    include_all_reviews?: boolean;
  }): Promise<PaginatedReviewsResponse> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return this.get(`/api/reviews/paginated?${searchParams.toString()}`);
  }

  async getReviewStats(params: {
    google_place_id?: string;
    start_date?: string;
    end_date?: string;
    include_all_reviews?: boolean;
  }): Promise<{
    total: number;
    averageRating: number;
    sentimentDistribution: Record<string, number>;
  }> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return this.get(`/api/reviews/stats?${searchParams.toString()}`);
  }

  // Summary Management Methods
  async generateSummary(params: {
    google_place_id: string;
    period_start: string;
    period_end: string;
    business_name?: string;
  }): Promise<{
    summary: Summary;
    details: {
      positive_themes: string[];
      negative_themes: string[];
      recommended_improvements: string[];
      total_reviews: number;
      sentiment_distribution: Record<string, number>;
      key_insights: string[];
      overall_rating_trend: string;
    };
  }> {
    return this.post("/api/summaries/generate", params);
  }

  async getSummaries(): Promise<Summary[]> {
    return this.get("/api/summaries");
  }
}

export const apiClient = new ApiClient();




