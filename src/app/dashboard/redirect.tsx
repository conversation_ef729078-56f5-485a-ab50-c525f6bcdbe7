'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useBusinesses } from '@/hooks/useBusinesses';

export default function DashboardRedirect() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const { businesses, isLoading: businessesLoading } = useBusinesses();

  useEffect(() => {
    if (!authLoading && !businessesLoading) {
      if (!user) {
        router.push('/login');
      } else if (businesses.length === 0) {
        // No businesses, redirect to onboarding
        router.push('/onboarding');
      } else {
        // Has businesses, redirect to overview
        router.push('/dashboard/overview');
      }
    }
  }, [user, businesses, authLoading, businessesLoading, router]);

  if (authLoading || businessesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return null;
}
