"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useBusinesses } from "@/hooks/useBusinesses";
import { supabase } from "@/lib/supabase";
import { apiClient } from "@/lib/api";

export default function DebugBusinessesPage() {
  const { user, session, loading: authLoading } = useAuth();
  const { businesses, isLoading, error, refetch } = useBusinesses();
  const [manualTestResult, setManualTestResult] = useState<any>(null);
  const [manualTestError, setManualTestError] = useState<string | null>(null);

  // Manual test function
  const runManualTest = async () => {
    try {
      console.log("🔍 Manual test: Starting...");
      setManualTestError(null);
      setManualTestResult(null);

      // Check session
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      console.log("🔍 Manual test: Session exists:", !!currentSession);
      console.log("🔍 Manual test: Access token exists:", !!currentSession?.access_token);

      if (!currentSession?.access_token) {
        throw new Error("No access token available");
      }

      // Call API directly
      const result = await apiClient.getMyBusinesses();
      console.log("✅ Manual test: Success, received:", result.length, "businesses");
      setManualTestResult(result);
    } catch (err) {
      console.error("❌ Manual test: Error:", err);
      setManualTestError(err instanceof Error ? err.message : "Unknown error");
    }
  };

  useEffect(() => {
    console.log("🔍 Debug page: Auth state changed");
    console.log("🔍 Debug page: Auth loading:", authLoading);
    console.log("🔍 Debug page: User:", user?.id, user?.email);
    console.log("🔍 Debug page: Session exists:", !!session);
    console.log("🔍 Debug page: Access token exists:", !!session?.access_token);
  }, [authLoading, user, session]);

  useEffect(() => {
    console.log("🔍 Debug page: Businesses state changed");
    console.log("🔍 Debug page: Businesses loading:", isLoading);
    console.log("🔍 Debug page: Businesses error:", error);
    console.log("🔍 Debug page: Businesses count:", businesses.length);
    console.log("🔍 Debug page: Businesses data:", businesses);
  }, [isLoading, error, businesses]);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold">Debug Businesses Page</h1>

        {/* Auth Status */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Auth Loading:</strong> {authLoading ? "Yes" : "No"}</p>
            <p><strong>User ID:</strong> {user?.id || "None"}</p>
            <p><strong>User Email:</strong> {user?.email || "None"}</p>
            <p><strong>Session Exists:</strong> {session ? "Yes" : "No"}</p>
            <p><strong>Access Token Exists:</strong> {session?.access_token ? "Yes" : "No"}</p>
          </div>
        </div>

        {/* useBusinesses Hook Status */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">useBusinesses Hook Status</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {isLoading ? "Yes" : "No"}</p>
            <p><strong>Error:</strong> {error ? error.message : "None"}</p>
            <p><strong>Businesses Count:</strong> {businesses.length}</p>
            <p><strong>Businesses Data:</strong></p>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(businesses, null, 2)}
            </pre>
          </div>
          <button
            onClick={() => refetch()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refetch Businesses
          </button>
        </div>

        {/* Manual Test */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Manual API Test</h2>
          <button
            onClick={runManualTest}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 mb-4"
          >
            Run Manual Test
          </button>
          
          {manualTestError && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <strong>Manual Test Error:</strong> {manualTestError}
            </div>
          )}
          
          {manualTestResult && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <strong>Manual Test Success:</strong> Received {manualTestResult.length} businesses
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto mt-2">
                {JSON.stringify(manualTestResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Raw Session Data */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Raw Session Data</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
