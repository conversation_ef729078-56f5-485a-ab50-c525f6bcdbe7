import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { BusinessService } from '@/lib/services/business';
import { authenticateUser, createAuthUser } from '@/lib/auth-server';
import { locales } from '@/i18n/config';

const searchRequestSchema = z.object({
  google_maps_url: z.string().url('Invalid URL format'),
  language: z.enum(locales).optional().default('en'),
});

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const { user, supabaseAdmin } = await authenticateUser(request);

    if (!user || !supabaseAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const { google_maps_url, language } = searchRequestSchema.parse(body);

    // Get Apify API token from environment
    const apifyApiToken = process.env.APIFY_API_TOKEN;
    if (!apifyApiToken) {
      return NextResponse.json(
        { error: 'Apify API token not configured' },
        { status: 500 }
      );
    }

    // Initialize business service
    const businessService = new BusinessService(apifyApiToken);

    // Create AuthUser object from Supabase user
    const authUser = createAuthUser(user);

    // Search for business
    const businessInfo = await businessService.searchFromGoogleMapsUrl(google_maps_url, authUser, language);

    return NextResponse.json(businessInfo);
  } catch (error) {
    console.error('Error in business search:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.issues },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required') {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
