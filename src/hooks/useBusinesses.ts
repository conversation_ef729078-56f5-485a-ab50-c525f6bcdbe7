"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";

export function useBusinesses() {
  const queryClient = useQueryClient();
  const { user, session, loading: authLoading } = useAuth();

  // Get saved businesses
  const {
    data: businesses = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["businesses"],
    queryFn: async () => {
      console.log("🔍 useBusinesses: Starting API call");
      console.log("🔍 useBusinesses: Auth loading:", authLoading);
      console.log("🔍 useBusinesses: User:", user?.id, user?.email);
      console.log("🔍 useBusinesses: Session exists:", !!session);
      console.log("🔍 useBusinesses: Access token exists:", !!session?.access_token);

      // Always call the API client directly - let it handle the session
      try {
        const result = await apiClient.getMyBusinesses();
        console.log("✅ useBusinesses: API call successful, received:", result.length, "businesses");
        console.log("✅ useBusinesses: API result data:", result);
        return result;
      } catch (apiError) {
        console.error("❌ useBusinesses: API call failed:", apiError);
        throw apiError;
      }
    },
    enabled: !authLoading && !!user, // Simplified condition - just check if auth is loaded and user exists
    retry: 1,
    staleTime: 0, // Always refetch for debugging
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  console.log("🔍 useBusinesses: Hook state summary:");
  console.log("  - Auth loading:", authLoading);
  console.log("  - User exists:", !!user);
  console.log("  - Session exists:", !!session);
  console.log("  - Access token exists:", !!session?.access_token);
  console.log("  - Query enabled:", !authLoading && !!user && !!session?.access_token);
  console.log("  - Query loading:", isLoading);
  console.log("  - Query error:", error?.message);
  console.log("  - Final businesses array length:", businesses.length);
  console.log("  - Final businesses array:", JSON.stringify(businesses, null, 2));

  // Search business from URL
  const searchFromUrlMutation = useMutation({
    mutationFn: apiClient.searchBusinessFromUrl,
  });

  // Save business
  const saveBusinessMutation = useMutation({
    mutationFn: apiClient.saveBusiness,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["businesses"] });
    },
  });

  // Remove business
  const removeBusinessMutation = useMutation({
    mutationFn: apiClient.removeBusiness,
    onSuccess: () => {
  
      queryClient.invalidateQueries({ queryKey: ["businesses"] });
    },
  });

  // This functionality is now handled by the reviews API
  // Remove the fetchReviewsByBusiness mutation as it's no longer needed

  return {
    // Data
    businesses,
    isLoading,
    error,

    // Actions
    searchFromUrl: searchFromUrlMutation.mutateAsync,
    saveBusiness: saveBusinessMutation.mutateAsync,
    removeBusiness: removeBusinessMutation.mutateAsync,
    refetch,

    // Loading states
    isSearching: searchFromUrlMutation.isPending,
    isSaving: saveBusinessMutation.isPending,
    isRemoving: removeBusinessMutation.isPending,

    // Errors
    searchError: searchFromUrlMutation.error,
    saveError: saveBusinessMutation.error,
    removeError: removeBusinessMutation.error,
  };
}
